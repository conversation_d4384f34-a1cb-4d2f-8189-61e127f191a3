// 安全图片组件
Component({
  // 外部样式类
  externalClasses: ['custom-class', 'image-class'],

  properties: {
    // 图片源地址
    src: {
      type: String,
      value: ''
    },
    // 默认图片
    defaultSrc: {
      type: String,
      value: '/assets/images/default-dish.png'
    },
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 图片类名
    className: {
      type: String,
      value: ''
    },
    // 图片样式
    style: {
      type: String,
      value: ''
    },
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      value: true
    },
    // 是否懒加载
    lazyLoad: {
      type: Boolean,
      value: true
    },
    // 图片类型（用于选择默认图片）
    imageType: {
      type: String,
      value: 'dish' // dish, avatar, menu, general
    },
    // 是否可以预览
    previewable: {
      type: Boolean,
      value: false
    },
    // 图片宽度（用于压缩优化）
    width: {
      type: Number,
      value: 0
    },
    // 图片高度（用于压缩优化）
    height: {
      type: Number,
      value: 0
    },
    // 图片质量 (0-100)
    quality: {
      type: Number,
      value: 80
    },
    // 是否启用渐进式加载
    progressive: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 当前显示的图片地址
    currentSrc: '',
    // 加载状态
    loading: true,
    // 是否加载失败
    error: false,
    // 重试次数
    retryCount: 0,
    // 最大重试次数
    maxRetry: 2,
    // 优化后的图片URL
    optimizedSrc: '',
    // 是否在视口内
    inViewport: false,
    // 加载进度
    loadProgress: 0,
    // 缓存键
    cacheKey: ''
  },

  observers: {
    'src, imageType': function (src, imageType) {
      this.loadImage(src, imageType);
    }
  },

  lifetimes: {
    attached() {
      this.initImageCache();
      if (!this.data.lazyLoad) {
        this.loadImage(this.data.src, this.data.imageType);
      } else {
        this.setupIntersectionObserver();
      }
    },

    detached() {
      this.destroyIntersectionObserver();
    }
  },

  methods: {
    /**
     * 初始化图片缓存
     */
    initImageCache() {
      if (!wx.getStorageSync('imageCache')) {
        wx.setStorageSync('imageCache', {});
      }
    },

    /**
     * 获取缓存键
     */
    getCacheKey(src) {
      return `img_${src.replace(/[^a-zA-Z0-9]/g, '_')}`;
    },

    /**
     * 获取缓存的图片
     */
    getCachedImage(src) {
      try {
        const cache = wx.getStorageSync('imageCache') || {};
        const cacheKey = this.getCacheKey(src);
        const cached = cache[cacheKey];

        if (cached && cached.timestamp) {
          // 缓存有效期24小时
          const isValid = Date.now() - cached.timestamp < 24 * 60 * 60 * 1000;
          if (isValid) {
            return cached.data;
          }
        }
      } catch (error) {
        console.warn('获取图片缓存失败:', error);
      }
      return null;
    },

    /**
     * 设置图片缓存
     */
    setCachedImage(src, data) {
      try {
        const cache = wx.getStorageSync('imageCache') || {};
        const cacheKey = this.getCacheKey(src);
        cache[cacheKey] = {
          data,
          timestamp: Date.now()
        };
        wx.setStorageSync('imageCache', cache);
      } catch (error) {
        console.warn('设置图片缓存失败:', error);
      }
    },

    /**
     * 获取默认图片
     */
    getDefaultImage(imageType) {
      const defaultImages = {
        dish: '/assets/images/default-dish.png',
        avatar: '/assets/images/default-avatar.png',
        menu: '/assets/images/default-menu.png',
        general: '/assets/images/default-image.png'
      };

      return defaultImages[imageType] || this.data.defaultSrc;
    },

    /**
     * 优化图片URL
     */
    optimizeImageUrl(src) {
      if (!src || src.startsWith('/assets/') || src.startsWith('data:')) {
        return src;
      }

      // 如果是jsdelivr CDN，添加压缩参数
      if (src.includes('cdn.jsdelivr.net')) {
        const {width, height, quality} = this.data;
        let optimizedUrl = src;

        // 添加压缩参数（如果支持）
        if (width > 0 || height > 0) {
          const params = [];
          if (width > 0) params.push(`w=${width}`);
          if (height > 0) params.push(`h=${height}`);
          if (quality < 100) params.push(`q=${quality}`);

          if (params.length > 0) {
            optimizedUrl += (src.includes('?') ? '&' : '?') + params.join('&');
          }
        }

        return optimizedUrl;
      }

      return src;
    },

    /**
     * 设置视口观察器
     */
    setupIntersectionObserver() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
      }

      this.intersectionObserver = this.createIntersectionObserver({
        rootMargin: '100px'
      });

      this.intersectionObserver
        .relativeToViewport()
        .observe('.safe-image-container', res => {
          if (res.intersectionRatio > 0 && !this.data.inViewport) {
            this.setData({inViewport: true});
            this.loadImage(this.data.src, this.data.imageType);
          }
        });
    },

    /**
     * 销毁视口观察器
     */
    destroyIntersectionObserver() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
        this.intersectionObserver = null;
      }
    },

    /**
     * 加载图片
     */
    loadImage(src, imageType) {
      // 重置状态
      this.setData({
        loading: true,
        error: false,
        retryCount: 0,
        loadProgress: 0
      });

      // 如果没有图片地址，直接使用默认图片
      if (!src || src.trim() === '') {
        this.useDefaultImage(imageType);
        return;
      }

      // 检查缓存
      const cachedSrc = this.getCachedImage(src);
      if (cachedSrc) {
        console.log('🎯 使用缓存图片:', src);
        this.setData({
          currentSrc: cachedSrc,
          optimizedSrc: cachedSrc,
          loading: false
        });
        return;
      }

      // 优化图片URL
      const optimizedSrc = this.optimizeImageUrl(src);

      // 设置当前图片地址
      this.setData({
        currentSrc: optimizedSrc,
        optimizedSrc: optimizedSrc,
        cacheKey: this.getCacheKey(src)
      });

      // 如果启用渐进式加载，先显示低质量版本
      if (this.data.progressive && this.data.quality > 30) {
        this.loadProgressiveImage(src, optimizedSrc);
      }
    },

    /**
     * 渐进式图片加载
     */
    loadProgressiveImage(originalSrc, optimizedSrc) {
      // 先加载低质量版本
      const lowQualitySrc = this.optimizeImageUrl(originalSrc).replace(
        /q=\d+/,
        'q=30'
      );

      if (lowQualitySrc !== optimizedSrc) {
        // 预加载低质量图片
        wx.getImageInfo({
          src: lowQualitySrc,
          success: () => {
            this.setData({
              currentSrc: lowQualitySrc,
              loadProgress: 50
            });

            // 然后加载高质量图片
            setTimeout(() => {
              this.setData({
                currentSrc: optimizedSrc,
                loadProgress: 100
              });
            }, 100);
          },
          fail: () => {
            // 低质量图片加载失败，直接加载原图
            this.setData({
              currentSrc: optimizedSrc
            });
          }
        });
      }
    },

    /**
     * 使用默认图片
     */
    useDefaultImage(imageType) {
      const defaultSrc = this.getDefaultImage(imageType);
      console.log('🖼️ 使用默认图片:', defaultSrc);

      this.setData({
        currentSrc: defaultSrc,
        loading: false,
        error: false
      });
    },

    /**
     * 图片加载成功
     */
    onImageLoad(e) {
      console.log('✅ 图片加载成功:', this.data.currentSrc);

      // 缓存成功加载的图片
      if (this.data.src && this.data.currentSrc) {
        this.setCachedImage(this.data.src, this.data.currentSrc);
      }

      this.setData({
        loading: false,
        error: false,
        loadProgress: 100
      });

      // 触发加载成功事件
      this.triggerEvent('load', {
        src: this.data.currentSrc,
        detail: e.detail
      });
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      console.warn('❌ 图片加载失败:', this.data.currentSrc, e.detail);

      const {retryCount, maxRetry} = this.data;

      // 如果还可以重试
      if (retryCount < maxRetry) {
        console.log(
          `🔄 重试加载图片 (${retryCount + 1}/${maxRetry}):`,
          this.data.currentSrc
        );

        this.setData({
          retryCount: retryCount + 1
        });

        // 延迟重试
        setTimeout(() => {
          this.setData({
            currentSrc:
              this.data.src + `?retry=${this.data.retryCount}&t=${Date.now()}`
          });
        }, 1000 * (retryCount + 1)); // 递增延迟

        return;
      }

      // 重试次数用完，使用默认图片
      console.log('🔄 重试次数用完，使用默认图片');
      this.useDefaultImage(this.data.imageType);

      // 触发加载失败事件
      this.triggerEvent('error', {
        src: this.data.src,
        error: e.detail,
        retryCount: this.data.retryCount
      });
    },

    /**
     * 图片点击事件
     */
    onImageTap(e) {
      // 如果可以预览且图片加载成功
      if (this.data.previewable && !this.data.error && !this.data.loading) {
        wx.previewImage({
          current: this.data.currentSrc,
          urls: [this.data.currentSrc]
        });
      }

      // 触发点击事件
      this.triggerEvent('tap', {
        src: this.data.currentSrc,
        detail: e.detail
      });
    },

    /**
     * 手动重新加载
     */
    reload() {
      console.log('🔄 手动重新加载图片');
      this.loadImage(this.data.src, this.data.imageType);
    }
  }
});

// 安全图片组件 - 增强版
Component({
  // 外部样式类
  externalClasses: ['custom-class', 'image-class'],

  properties: {
    // 图片源地址
    src: {
      type: String,
      value: ''
    },
    // 默认图片
    defaultSrc: {
      type: String,
      value: '/assets/images/default-dish.png'
    },
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 图片类名
    className: {
      type: String,
      value: ''
    },
    // 图片样式
    style: {
      type: String,
      value: ''
    },
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      value: true
    },
    // 是否懒加载
    lazyLoad: {
      type: Boolean,
      value: true
    },
    // 图片类型（用于选择默认图片）
    imageType: {
      type: String,
      value: 'dish' // dish, avatar, menu, general
    },
    // 是否可以预览
    previewable: {
      type: Boolean,
      value: false
    },
    // 是否启用WebP优化
    enableWebp: {
      type: Boolean,
      value: true
    },
    // 图片质量 (0-100)
    quality: {
      type: Number,
      value: 80
    },
    // 图片宽度（用于压缩）
    width: {
      type: Number,
      value: 0
    },
    // 图片高度（用于压缩）
    height: {
      type: Number,
      value: 0
    },
    // 是否启用渐进式加载
    progressive: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 当前显示的图片地址
    currentSrc: '',
    // 加载状态
    loading: true,
    // 是否加载失败
    error: false,
    // 重试次数
    retryCount: 0,
    // 最大重试次数
    maxRetry: 2,
    // 是否支持WebP
    supportsWebp: false,
    // 优化后的图片URL
    optimizedSrc: '',
    // 加载进度
    loadProgress: 0,
    // 是否在视口内
    inViewport: false
  },

  observers: {
    'src, imageType': function (src, imageType) {
      this.loadImage(src, imageType);
    }
  },

  lifetimes: {
    attached() {
      this.checkWebpSupport();
      this.setupIntersectionObserver();
      if (!this.data.lazyLoad) {
        this.loadImage(this.data.src, this.data.imageType);
      }
    },

    detached() {
      this.destroyIntersectionObserver();
    }
  },

  methods: {
    /**
     * 获取默认图片
     */
    getDefaultImage(imageType) {
      const defaultImages = {
        dish: '/assets/images/default-dish.png',
        avatar: '/assets/images/default-avatar.png',
        menu: '/assets/images/default-menu.png',
        general: '/assets/images/default-image.png'
      };

      return defaultImages[imageType] || this.data.defaultSrc;
    },

    /**
     * 加载图片
     */
    loadImage(src, imageType) {
      // 重置状态
      this.setData({
        loading: true,
        error: false,
        retryCount: 0
      });

      // 如果没有图片地址，直接使用默认图片
      if (!src || src.trim() === '') {
        this.useDefaultImage(imageType);
        return;
      }

      // 设置当前图片地址
      this.setData({
        currentSrc: src,
        loading: false
      });
    },

    /**
     * 使用默认图片
     */
    useDefaultImage(imageType) {
      const defaultSrc = this.getDefaultImage(imageType);
      console.log('🖼️ 使用默认图片:', defaultSrc);

      this.setData({
        currentSrc: defaultSrc,
        loading: false,
        error: false
      });
    },

    /**
     * 图片加载成功
     */
    onImageLoad(e) {
      console.log('✅ 图片加载成功:', this.data.currentSrc);

      this.setData({
        loading: false,
        error: false
      });

      // 触发加载成功事件
      this.triggerEvent('load', {
        src: this.data.currentSrc,
        detail: e.detail
      });
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      console.warn('❌ 图片加载失败:', this.data.currentSrc, e.detail);

      const {retryCount, maxRetry} = this.data;

      // 如果还可以重试
      if (retryCount < maxRetry) {
        console.log(
          `🔄 重试加载图片 (${retryCount + 1}/${maxRetry}):`,
          this.data.currentSrc
        );

        this.setData({
          retryCount: retryCount + 1
        });

        // 延迟重试
        setTimeout(() => {
          this.setData({
            currentSrc:
              this.data.src + `?retry=${this.data.retryCount}&t=${Date.now()}`
          });
        }, 1000 * (retryCount + 1)); // 递增延迟

        return;
      }

      // 重试次数用完，使用默认图片
      console.log('🔄 重试次数用完，使用默认图片');
      this.useDefaultImage(this.data.imageType);

      // 触发加载失败事件
      this.triggerEvent('error', {
        src: this.data.src,
        error: e.detail,
        retryCount: this.data.retryCount
      });
    },

    /**
     * 图片点击事件
     */
    onImageTap(e) {
      // 如果可以预览且图片加载成功
      if (this.data.previewable && !this.data.error && !this.data.loading) {
        wx.previewImage({
          current: this.data.currentSrc,
          urls: [this.data.currentSrc]
        });
      }

      // 触发点击事件
      this.triggerEvent('tap', {
        src: this.data.currentSrc,
        detail: e.detail
      });
    },

    /**
     * 手动重新加载
     */
    reload() {
      console.log('🔄 手动重新加载图片');
      this.loadImage(this.data.src, this.data.imageType);
    }
  }
});

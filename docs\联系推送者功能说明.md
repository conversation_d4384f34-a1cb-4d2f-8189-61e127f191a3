# 联系推送者功能说明

## 🎯 功能概述

在订单详情页面，如果订单是被其他用户推送的，用户可以通过"联系"按钮直接拨打推送者的电话。

## 🔧 技术实现

### 后端修改

#### 1. 数据库结构
- `User` 表中的 `phone` 字段存储用户电话号码
- `OrderPush` 表关联推送者和订单信息

#### 2. API 优化
**文件**: `webs/server/src/controllers/orderPushController.js`

```javascript
// 在 getVisibleOrders 方法中，增加了 pusher 的 phone 字段
orderPushes: {
  where: {targetUserId: userId},
  include: {
    pusher: {
      select: {id: true, name: true, phone: true, avatar: true}
    }
  }
}
```

### 前端修改

#### 1. 小程序权限配置
**文件**: `app.json`

```json
{
  "requiredPrivateInfos": ["getLocation", "makePhoneCall"]
}
```

#### 2. 联系功能实现
**文件**: `pages/order_detail/index.js`

```javascript
contactPusher() {
  const {order} = this.data;

  if (order.isPushedToMe && order.pushedBy) {
    const pusher = order.pushedBy;
    
    // 检查推送者是否有电话号码
    if (pusher.phone) {
      wx.showModal({
        title: '联系推送者',
        content: `拨打电话给 ${pusher.name}？\n电话：${pusher.phone}`,
        confirmText: '拨打',
        cancelText: '取消',
        success: res => {
          if (res.confirm) {
            // 调用微信打电话API
            wx.makePhoneCall({
              phoneNumber: pusher.phone,
              success: () => {
                console.log('✅ 拨打电话成功');
              },
              fail: error => {
                console.error('❌ 拨打电话失败:', error);
                wx.showToast({
                  title: '拨打失败',
                  icon: 'error'
                });
              }
            });
          }
        }
      });
    } else {
      // 没有电话号码时的提示
      wx.showModal({
        title: '联系推送者',
        content: `${pusher.name} 暂无联系方式`,
        confirmText: '知道了',
        showCancel: false
      });
    }
  }
}
```

## 📱 用户界面

### 订单详情页面
- 推送信息区域显示推送者姓名
- "联系"按钮位于推送者姓名旁边
- 点击后根据是否有电话号码显示不同的弹窗

### 交互流程

#### 情况1：推送者有电话号码
1. 用户点击"联系"按钮
2. 显示确认弹窗，包含推送者姓名和电话号码
3. 用户确认后调用 `wx.makePhoneCall` 拨打电话
4. 成功拨打或显示错误提示

#### 情况2：推送者无电话号码
1. 用户点击"联系"按钮
2. 显示提示弹窗："XXX 暂无联系方式"
3. 用户点击"知道了"关闭弹窗

## 🧪 测试步骤

### 准备工作
1. 确保有两个测试用户账号
2. 两个用户之间建立关联关系
3. 其中一个用户设置了电话号码

### 测试流程
1. **用户A** 创建订单并推送给 **用户B**
2. **用户B** 登录小程序，查看订单列表
3. **用户B** 点击被推送的订单，进入订单详情页
4. 验证推送信息显示正确
5. 点击"联系"按钮

### 预期结果

#### 有电话号码的情况
- 显示确认弹窗，包含推送者姓名和电话号码
- 点击"拨打"后调用系统拨号功能
- 可以正常拨打电话

#### 无电话号码的情况
- 显示提示弹窗："XXX 暂无联系方式"
- 点击"知道了"关闭弹窗

## 🔒 权限说明

### 小程序权限
- `makePhoneCall`: 拨打电话权限
- 需要在 `app.json` 中声明 `requiredPrivateInfos`

### 用户授权
- 首次使用拨打电话功能时，微信会提示用户授权
- 用户可以在小程序设置中管理权限

## 🛡️ 安全考虑

### 数据保护
- 电话号码仅在需要时返回给前端
- 只有关联用户才能看到推送订单
- 电话号码不会在日志中明文记录

### 权限控制
- 只有被推送订单的接收者才能看到"联系"按钮
- 推送者必须是已关联的用户
- 电话号码字段可选，用户可以选择不填写

## 🐛 故障排除

### 常见问题

1. **点击联系按钮无反应**
   - 检查订单是否为推送订单
   - 确认 `order.isPushedToMe` 和 `order.pushedBy` 数据正确

2. **显示"暂无联系方式"**
   - 推送者未设置电话号码
   - 检查后端是否正确返回 phone 字段

3. **拨打电话失败**
   - 检查小程序权限配置
   - 确认用户已授权拨打电话权限
   - 检查电话号码格式是否正确

### 调试方法
```javascript
// 在控制台查看订单数据
console.log('订单数据:', this.data.order);
console.log('推送者信息:', this.data.order.pushedBy);
console.log('推送者电话:', this.data.order.pushedBy?.phone);
```

## 📈 后续优化建议

1. **联系方式扩展**
   - 支持微信聊天
   - 支持发送短信
   - 支持邮件联系

2. **用户体验优化**
   - 添加联系历史记录
   - 支持快速联系常用联系人
   - 添加联系方式验证

3. **隐私保护增强**
   - 支持隐藏电话号码部分数字
   - 添加联系权限设置
   - 支持临时联系码

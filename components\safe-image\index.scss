.safe-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  // 默认宽高 200rpx
  width: 200rpx;
  height: 200rpx;

  .safe-image {
    width: 100%;
    height: 100%;
    display: block;
    transition:
      opacity 0.3s ease,
      filter 0.3s ease;

    &.progressive-loading {
      opacity: 0.7;
      filter: blur(1px);
    }
  }

  .safe-image-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;

    .loading-text {
      font-size: 24rpx;
      color: #999;
      margin-top: 16rpx;
    }

    .load-progress {
      margin-top: 8rpx;

      .progress-text {
        font-size: 20rpx;
        color: #666;
        background: rgba(255, 255, 255, 0.9);
        padding: 2rpx 8rpx;
        border-radius: 4rpx;
      }
    }
  }

  .safe-image-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;

    .error-text {
      font-size: 24rpx;
      color: #ccc;
      margin: 16rpx 0;
    }

    .retry-btn {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 8rpx;
      font-size: 24rpx;
      color: #666;

      text {
        margin-left: 8rpx;
      }

      &:active {
        background: rgba(0, 0, 0, 0.2);
      }
    }
  }

  .retry-indicator {
    position: absolute;
    bottom: 8rpx;
    right: 8rpx;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    font-size: 20rpx;
    z-index: 2;
  }

  .cache-indicator {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 50%;
    padding: 4rpx;
    z-index: 2;
  }
}

/* 常用尺寸预设 */
.safe-image-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.safe-image-dish {
  width: 200rpx;
  height: 150rpx;
  border-radius: 12rpx;
}

.safe-image-menu {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
}

.safe-image-thumbnail {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

/* 加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.safe-image {
  animation: fadeIn 0.3s ease;
}

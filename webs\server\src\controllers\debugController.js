const prisma = require('../utils/prisma');
const { success, error } = require('../utils/response');

/**
 * 调试用户openid状态
 * @route GET /api/debug/users/openid
 */
const checkUsersOpenid = async (req, res) => {
  try {
    // 获取所有用户的openid信息
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        phone: true,
        openid: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // 分析openid状态
    const analysis = users.map(user => ({
      id: user.id,
      name: user.name,
      phone: user.phone,
      openid: user.openid,
      openidStatus: getOpenidStatus(user.openid),
      canReceiveNotification: canReceiveWechatNotification(user.openid),
      createdAt: user.createdAt
    }));

    // 统计信息
    const stats = {
      total: users.length,
      hasValidOpenid: analysis.filter(u => u.canReceiveNotification).length,
      hasMockOpenid: analysis.filter(u => u.openidStatus === 'mock').length,
      hasNoOpenid: analysis.filter(u => u.openidStatus === 'none').length
    };

    return success(res, {
      users: analysis,
      stats
    });
  } catch (err) {
    console.error('Check users openid error:', err);
    return error(res, 'Failed to check users openid', 500);
  }
};

/**
 * 调试订单推送通知
 * @route POST /api/debug/order-push/notification
 */
const debugOrderPushNotification = async (req, res) => {
  try {
    const { fromUserId, toUserId, orderId } = req.body;

    if (!fromUserId || !toUserId) {
      return error(res, 'fromUserId and toUserId are required', 400);
    }

    // 获取发送者信息
    const fromUser = await prisma.user.findUnique({
      where: { id: fromUserId },
      select: { id: true, name: true, openid: true }
    });

    // 获取接收者信息
    const toUser = await prisma.user.findUnique({
      where: { id: toUserId },
      select: { id: true, name: true, openid: true }
    });

    if (!fromUser || !toUser) {
      return error(res, 'User not found', 404);
    }

    // 检查关联关系
    const connection = await prisma.userConnection.findFirst({
      where: {
        OR: [
          { senderId: fromUserId, receiverId: toUserId, status: 'accepted' },
          { senderId: toUserId, receiverId: fromUserId, status: 'accepted' }
        ]
      }
    });

    // 分析结果
    const analysis = {
      fromUser: {
        ...fromUser,
        openidStatus: getOpenidStatus(fromUser.openid),
        canSend: true
      },
      toUser: {
        ...toUser,
        openidStatus: getOpenidStatus(toUser.openid),
        canReceive: canReceiveWechatNotification(toUser.openid)
      },
      connection: {
        exists: !!connection,
        status: connection?.status || 'none',
        canPush: !!connection && connection.status === 'accepted'
      },
      notification: {
        willSend: !!connection && 
                  connection.status === 'accepted' && 
                  canReceiveWechatNotification(toUser.openid),
        reason: getNotificationBlockReason(connection, toUser.openid)
      }
    };

    // 如果提供了订单ID，获取订单信息
    if (orderId) {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          user: { select: { id: true, name: true } },
          orderPushes: {
            include: {
              pusher: { select: { id: true, name: true } },
              targetUser: { select: { id: true, name: true } }
            }
          }
        }
      });

      analysis.order = order;
    }

    return success(res, analysis);
  } catch (err) {
    console.error('Debug order push notification error:', err);
    return error(res, 'Failed to debug order push notification', 500);
  }
};

/**
 * 获取openid状态
 */
function getOpenidStatus(openid) {
  if (!openid) return 'none';
  if (openid.startsWith('mock_')) return 'mock';
  return 'valid';
}

/**
 * 检查是否可以接收微信通知
 */
function canReceiveWechatNotification(openid) {
  return openid && !openid.startsWith('mock_');
}

/**
 * 获取通知被阻止的原因
 */
function getNotificationBlockReason(connection, openid) {
  if (!connection) {
    return '用户之间没有关联关系';
  }
  
  if (connection.status !== 'accepted') {
    return `关联状态不正确: ${connection.status}`;
  }
  
  if (!openid) {
    return '接收者没有openid';
  }
  
  if (openid.startsWith('mock_')) {
    return '接收者使用的是模拟openid，无法发送真实微信通知';
  }
  
  return '无阻止原因，应该可以发送';
}

/**
 * 测试微信通知发送
 * @route POST /api/debug/wechat/test-notification
 */
const testWechatNotification = async (req, res) => {
  try {
    const { userId, message = '测试通知' } = req.body;

    if (!userId) {
      return error(res, 'userId is required', 400);
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, name: true, openid: true }
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    const analysis = {
      user: {
        ...user,
        openidStatus: getOpenidStatus(user.openid),
        canReceive: canReceiveWechatNotification(user.openid)
      },
      testResult: null
    };

    if (!canReceiveWechatNotification(user.openid)) {
      analysis.testResult = {
        success: false,
        reason: getNotificationBlockReason(null, user.openid)
      };
    } else {
      // 这里可以调用实际的微信通知发送测试
      analysis.testResult = {
        success: true,
        message: '用户具备接收微信通知的条件'
      };
    }

    return success(res, analysis);
  } catch (err) {
    console.error('Test wechat notification error:', err);
    return error(res, 'Failed to test wechat notification', 500);
  }
};

module.exports = {
  checkUsersOpenid,
  debugOrderPushNotification,
  testWechatNotification
};

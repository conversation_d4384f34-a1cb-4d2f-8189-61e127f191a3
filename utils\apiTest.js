/**
 * API测试工具
 * 用于测试菜品相关API接口
 */

const { dishApi } = require('../services/api');

const ApiTest = {
  /**
   * 测试获取我的菜品接口
   */
  async testGetMyDishes() {
    console.log('🧪 开始测试 getMyDishes 接口...');
    
    try {
      // 检查token
      const token = wx.getStorageSync('token') || wx.getStorageSync('userToken');
      if (!token) {
        console.error('❌ 未找到token，请先登录');
        return false;
      }
      
      console.log('✅ Token存在:', token.substring(0, 20) + '...');
      
      // 测试基本请求
      const result = await dishApi.getMyDishes({
        page: 1,
        size: 10
      });
      
      console.log('✅ API请求成功:', result);
      
      if (result && result.data) {
        console.log('📊 返回数据:', {
          total: result.data.total,
          listLength: result.data.list?.length || 0,
          page: result.data.page,
          size: result.data.size
        });
        return true;
      } else {
        console.warn('⚠️ 返回数据格式异常:', result);
        return false;
      }
      
    } catch (error) {
      console.error('❌ API请求失败:', error);
      
      // 详细错误分析
      if (error.code === 401) {
        console.error('🔐 认证失败，可能是token过期或无效');
      } else if (error.code === 403) {
        console.error('🚫 权限不足');
      } else if (error.code === 404) {
        console.error('🔍 接口不存在');
      } else if (error.code === 500) {
        console.error('💥 服务器内部错误');
      } else {
        console.error('🌐 网络或其他错误');
      }
      
      return false;
    }
  },

  /**
   * 测试网络连接
   */
  async testNetworkConnection() {
    console.log('🌐 测试网络连接...');
    
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          console.log('✅ 网络类型:', res.networkType);
          console.log('✅ 网络可用:', res.networkType !== 'none');
          resolve(res.networkType !== 'none');
        },
        fail: (error) => {
          console.error('❌ 获取网络状态失败:', error);
          resolve(false);
        }
      });
    });
  },

  /**
   * 测试API服务器连通性
   */
  async testServerConnection() {
    console.log('🔗 测试服务器连通性...');
    
    const { baseURL } = require('../config/env');
    console.log('📡 API地址:', baseURL);
    
    return new Promise((resolve) => {
      wx.request({
        url: `${baseURL}/dishes/categories`,
        method: 'GET',
        success: (res) => {
          console.log('✅ 服务器连接成功:', res.statusCode);
          resolve(true);
        },
        fail: (error) => {
          console.error('❌ 服务器连接失败:', error);
          resolve(false);
        }
      });
    });
  },

  /**
   * 完整的诊断测试
   */
  async runDiagnostics() {
    console.log('🔍 开始API诊断...');
    console.log('='.repeat(50));
    
    const results = {
      network: false,
      server: false,
      api: false
    };
    
    // 1. 测试网络连接
    results.network = await this.testNetworkConnection();
    
    // 2. 测试服务器连通性
    if (results.network) {
      results.server = await this.testServerConnection();
    }
    
    // 3. 测试API接口
    if (results.server) {
      results.api = await this.testGetMyDishes();
    }
    
    console.log('='.repeat(50));
    console.log('📋 诊断结果:');
    console.log('  网络连接:', results.network ? '✅' : '❌');
    console.log('  服务器连接:', results.server ? '✅' : '❌');
    console.log('  API接口:', results.api ? '✅' : '❌');
    
    if (results.api) {
      console.log('🎉 所有测试通过！');
    } else {
      console.log('⚠️ 存在问题，请检查上述错误信息');
    }
    
    return results;
  }
};

module.exports = ApiTest;

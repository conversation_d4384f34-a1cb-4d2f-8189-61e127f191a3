# Safe-Image 组件使用指南

## 🎯 功能特性

### ✅ 已解决的问题
1. **app.json 配置错误** - 移除了不支持的 `makePhoneCall` 权限
2. **默认图片缺失** - 添加了完整的默认图片资源
3. **错误状态优化** - 不同类型图片有专门的错误显示

### 🖼️ 默认图片资源

| 类型 | 文件 | 用途 | 尺寸 |
|------|------|------|------|
| 菜品 | `default-dish.svg` | 菜品图片加载失败 | 400x300 |
| 头像 | `default-avatar.svg` | 用户头像加载失败 | 120x120 |
| 菜单 | `default-menu.svg` | 菜单图片加载失败 | 600x400 |
| 通用 | `default-image.svg` | 其他图片加载失败 | 400x400 |

## 📖 使用方法

### 基础用法

```xml
<!-- 菜品图片 -->
<safe-image 
  src="{{dish.image}}" 
  imageType="dish"
  width="{{400}}"
  height="{{300}}"
  quality="{{80}}"
/>

<!-- 用户头像 -->
<safe-image 
  src="{{user.avatar}}" 
  imageType="avatar"
  width="{{120}}"
  height="{{120}}"
  quality="{{85}}"
  mode="aspectFill"
/>

<!-- 菜单图片 -->
<safe-image 
  src="{{menu.image}}" 
  imageType="menu"
  width="{{600}}"
  height="{{400}}"
  quality="{{75}}"
/>
```

### 高级配置

```xml
<safe-image 
  src="{{imageUrl}}" 
  imageType="dish"
  width="{{400}}"
  height="{{300}}"
  quality="{{80}}"
  progressive="{{true}}"
  lazyLoad="{{true}}"
  previewable="{{true}}"
  showLoading="{{true}}"
  custom-class="my-image-container"
  image-class="my-image"
  bind:load="onImageLoad"
  bind:error="onImageError"
/>
```

## 🎨 错误状态设计

### 视觉效果
- **菜品错误**: 橙色渐变背景 + 餐具图标
- **头像错误**: 蓝色渐变背景 + 用户图标  
- **菜单错误**: 绿色渐变背景 + 菜单图标
- **通用错误**: 灰色背景 + 图片图标

### 交互功能
- 显示具体的错误信息
- 提供重试按钮
- 支持点击重新加载

## 🔧 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| src | String | '' | 图片地址 |
| imageType | String | 'general' | 图片类型：dish/avatar/menu/general |
| width | Number | 0 | 图片宽度（用于压缩） |
| height | Number | 0 | 图片高度（用于压缩） |
| quality | Number | 80 | 图片质量 0-100 |
| progressive | Boolean | true | 渐进式加载 |
| lazyLoad | Boolean | true | 懒加载 |
| previewable | Boolean | false | 是否可预览 |
| showLoading | Boolean | true | 显示加载状态 |
| mode | String | 'aspectFill' | 图片模式 |

## 📱 在页面中使用

### 1. 在页面JSON中引入组件

```json
{
  "usingComponents": {
    "safe-image": "/components/safe-image/index"
  }
}
```

### 2. 在WXML中使用

```xml
<!-- 菜品列表 -->
<view wx:for="{{dishList}}" wx:key="id" class="dish-item">
  <safe-image 
    src="{{item.image}}" 
    imageType="dish"
    width="{{200}}"
    height="{{150}}"
    custom-class="dish-image-container"
  />
  <text>{{item.name}}</text>
</view>

<!-- 用户信息 -->
<view class="user-info">
  <safe-image 
    src="{{userInfo.avatar}}" 
    imageType="avatar"
    width="{{80}}"
    height="{{80}}"
    mode="aspectFill"
    custom-class="user-avatar"
  />
  <text>{{userInfo.name}}</text>
</view>
```

### 3. 在JS中处理事件

```javascript
Page({
  // 图片加载成功
  onImageLoad(e) {
    console.log('图片加载成功:', e.detail);
  },

  // 图片加载失败
  onImageError(e) {
    console.log('图片加载失败:', e.detail);
    
    if (e.detail.isDefaultImageError) {
      console.log('默认图片也加载失败');
    }
  }
});
```

## 🎯 最佳实践

### 1. 根据使用场景选择合适的类型

```xml
<!-- 菜品展示 -->
<safe-image imageType="dish" width="{{400}}" height="{{300}}" />

<!-- 用户头像 -->
<safe-image imageType="avatar" width="{{120}}" height="{{120}}" />

<!-- 菜单封面 -->
<safe-image imageType="menu" width="{{600}}" height="{{400}}" />
```

### 2. 设置合适的图片质量

```xml
<!-- 缩略图：低质量 -->
<safe-image quality="{{60}}" width="{{160}}" height="{{120}}" />

<!-- 详情页：高质量 -->
<safe-image quality="{{90}}" width="{{600}}" height="{{450}}" />
```

### 3. 启用性能优化功能

```xml
<!-- 列表中的图片：启用懒加载 -->
<safe-image lazyLoad="{{true}}" progressive="{{true}}" />

<!-- 首屏图片：禁用懒加载 -->
<safe-image lazyLoad="{{false}}" progressive="{{false}}" />
```

## 🐛 故障排除

### 常见问题

1. **图片不显示**
   - 检查 `src` 属性是否正确
   - 确认图片URL可访问
   - 查看控制台错误信息

2. **默认图片不显示**
   - 确认 SVG 文件存在于 `/assets/images/` 目录
   - 检查 `imageType` 属性设置

3. **加载很慢**
   - 设置合适的 `width`、`height` 和 `quality`
   - 启用 `progressive` 渐进式加载
   - 检查网络环境

### 调试方法

```javascript
// 在组件上添加事件监听
<safe-image 
  bind:load="onImageLoad"
  bind:error="onImageError"
/>

// 在页面JS中查看详细信息
onImageLoad(e) {
  console.log('加载成功:', e.detail);
},

onImageError(e) {
  console.log('加载失败:', e.detail);
}
```

## 🔄 更新日志

### v2.1.0 (当前版本)
- ✅ 修复 app.json 配置错误
- ✅ 添加完整的默认图片资源
- ✅ 优化错误状态显示
- ✅ 支持不同类型的错误样式
- ✅ 改进默认图片加载失败处理

### v2.0.0
- 🎉 图片优化和缓存功能
- 🎉 渐进式加载支持
- 🎉 智能重试机制

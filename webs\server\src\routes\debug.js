const express = require('express');
const router = express.Router();
const auth = require('../middlewares/auth');
const debugController = require('../controllers/debugController');

// 调试用户openid状态
router.get('/users/openid', auth, debugController.checkUsersOpenid);

// 调试订单推送通知
router.post('/order-push/notification', auth, debugController.debugOrderPushNotification);

// 测试微信通知发送
router.post('/wechat/test-notification', auth, debugController.testWechatNotification);

module.exports = router;
